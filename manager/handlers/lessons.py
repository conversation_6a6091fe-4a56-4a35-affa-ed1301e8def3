from aiogram import Router, F, types
from aiogram.types import CallbackQuery, Message
from aiogram.filters import StateFilter
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
import logging

from manager.keyboards.lessons import (
    get_lessons_menu_kb,
    get_courses_list_kb,
    get_subjects_list_kb,
    get_lessons_list_kb,
    confirm_delete_lesson_kb,
    confirm_edit_lesson_kb,
    LessonCallback,
    LessonActions
)
from common.keyboards import get_home_kb
from database.repositories.lesson_repository import LessonRepository
from database.repositories.subject_repository import SubjectRepository
from database.repositories.course_repository import CourseRepository
from manager.handlers.main import ManagerMainStates

# Настройка логирования
logger = logging.getLogger(__name__)

router = Router()

class ManagerLessonStates(StatesGroup):
    main = State()  # Главное меню уроков (выбор курса)
    select_subject = State()  # Выбор предмета
    lessons_list = State()  # Список уроков предмета
    adding_lesson = State()  # Добавление нового урока
    confirm_deletion = State()  # Подтверждение удаления
    edit_lesson = State()  # Редактирование урока
    enter_new_lesson_name = State()  # Ввод нового названия урока
    confirm_edit_lesson = State()  # Подтверждение редактирования

@router.callback_query(F.data == "manager_lessons")
async def show_courses(callback: CallbackQuery, state: FSMContext):
    """Показываем список курсов"""
    # Получаем список курсов из БД
    courses = await CourseRepository.get_all()
    
    await callback.message.edit_text(
        text="Выберите курс для работы с уроками:",
        reply_markup=await get_courses_list_kb(courses)
    )
    await state.set_state(ManagerLessonStates.main)

@router.callback_query(LessonCallback.filter(F.action == LessonActions.VIEW), StateFilter(ManagerLessonStates.main, ManagerLessonStates.select_subject))
async def process_view_action(callback: CallbackQuery, callback_data: LessonCallback, state: FSMContext):
    """Обработка просмотра списков"""
    # Получаем course_id и subject_id из callback_data или состояния
    course_id = None
    subject_id = None

    if hasattr(callback_data, 'course_id') and callback_data.course_id is not None:
        course_id = callback_data.course_id
    else:
        # Если callback_data не содержит course_id, берем из состояния
        user_data = await state.get_data()
        course_id = user_data.get('course_id')

    if hasattr(callback_data, 'subject_id') and callback_data.subject_id is not None:
        subject_id = callback_data.subject_id
    else:
        # Если callback_data не содержит subject_id, берем из состояния
        user_data = await state.get_data()
        subject_id = user_data.get('subject_id')

    if course_id is not None and subject_id is None:
        # Показываем список предметов для курса
        course = await CourseRepository.get_by_id(course_id)
        if not course:
            await callback.message.edit_text(
                text="❌ Курс не найден!",
                reply_markup=get_home_kb()
            )
            return
            
        # Получаем предметы для курса
        subjects = await SubjectRepository.get_by_course(course_id)

        # Очищаем данные о предмете при выборе нового курса
        await state.update_data(course_id=course_id, course_name=course.name, subject_id=None, subject_name=None)
        await state.set_state(ManagerLessonStates.select_subject)
        await callback.message.edit_text(
            text=f"Выберите предмет из курса {course.name}:",
            reply_markup=await get_subjects_list_kb(subjects, course_id)
        )
    elif subject_id is not None:
        # Показываем список уроков для предмета
        subject = await SubjectRepository.get_by_id(subject_id)
        if not subject:
            await callback.message.edit_text(
                text="❌ Предмет не найден!",
                reply_markup=get_home_kb()
            )
            return
            
        # Получаем уроки для предмета и курса
        lessons = await LessonRepository.get_by_subject_and_course(subject_id, course_id)
        
        await state.update_data(
            subject_id=subject_id,
            subject_name=subject.name
        )
        await state.set_state(ManagerLessonStates.lessons_list)
        await callback.message.edit_text(
            text=f"📝 Список уроков по предмету {subject.name}:\n"
                 f"Всего уроков: {len(lessons)}",
            reply_markup=await get_lessons_list_kb(
                lessons,
                course_id=course_id,
                subject_id=subject_id
            )
        )

# Отдельные обработчики для навигации назад
async def back_to_select_subject(callback: CallbackQuery, state: FSMContext):
    """Обработчик для возврата к выбору предмета"""
    user_data = await state.get_data()
    course_id = user_data.get('course_id')
    course_name = user_data.get('course_name')

    if course_id:
        # Получаем предметы для курса
        subjects = await SubjectRepository.get_by_course(course_id)

        # Очищаем данные о предмете при возврате к выбору предмета
        await state.update_data(subject_id=None, subject_name=None)
        await state.set_state(ManagerLessonStates.select_subject)
        await callback.message.edit_text(
            text=f"Выберите предмет из курса {course_name}:",
            reply_markup=await get_subjects_list_kb(subjects, course_id)
        )

async def back_to_lessons_list(callback: CallbackQuery, state: FSMContext):
    """Обработчик для возврата к списку уроков"""
    user_data = await state.get_data()
    course_id = user_data.get('course_id')
    subject_id = user_data.get('subject_id')
    subject_name = user_data.get('subject_name')

    if course_id and subject_id and subject_name:
        # Получаем уроки для предмета и курса
        lessons = await LessonRepository.get_by_subject_and_course(subject_id, course_id)
        
        await state.set_state(ManagerLessonStates.lessons_list)
        await callback.message.edit_text(
            text=f"📝 Список уроков по предмету {subject_name}:\n"
                 f"Всего уроков: {len(lessons)}",
            reply_markup=await get_lessons_list_kb(
                lessons,
                course_id=course_id,
                subject_id=subject_id
            )
        )

@router.callback_query(LessonCallback.filter(F.action == LessonActions.ADD), StateFilter(ManagerLessonStates.lessons_list))
async def start_add_lesson(callback: CallbackQuery, callback_data: LessonCallback, state: FSMContext):
    """Начинаем процесс добавления урока"""
    data = await state.get_data()
    subject_name = data.get('subject_name')
    
    await state.set_state(ManagerLessonStates.adding_lesson)
    await callback.message.edit_text(
        text=f"Введите название нового урока для предмета {subject_name}:",
        reply_markup=get_home_kb()
    )

@router.message(StateFilter(ManagerLessonStates.adding_lesson))
async def process_lesson_name(message: Message, state: FSMContext):
    """Обрабатываем ввод названия урока"""
    data = await state.get_data()
    subject_id = data['subject_id']
    subject_name = data['subject_name']
    new_lesson_name = message.text.strip()
    
    try:
        # Создаем урок в базе данных
        lesson = await LessonRepository.create(new_lesson_name, subject_id, data['course_id'])

        # Получаем обновленный список уроков
        lessons = await LessonRepository.get_by_subject_and_course(subject_id, data['course_id'])
        
        await state.set_state(ManagerLessonStates.lessons_list)
        await message.answer(
            text=f"✅ Урок \"{new_lesson_name}\" успешно добавлен в предмет {subject_name}\n"
                 f"Всего уроков: {len(lessons)}",
            reply_markup=await get_lessons_list_kb(
                lessons,
                course_id=data['course_id'],
                subject_id=subject_id
            )
        )
    except ValueError as e:
        # Урок уже существует или другая ошибка
        await message.answer(
            text=f"❌ {str(e)}\n\nВведите другое название:",
            reply_markup=get_home_kb()
        )

@router.callback_query(LessonCallback.filter(F.action == LessonActions.DELETE), StateFilter(ManagerLessonStates.lessons_list))
async def confirm_delete(callback: CallbackQuery, callback_data: LessonCallback, state: FSMContext):
    """Запрашиваем подтверждение удаления урока"""
    lesson_id = callback_data.lesson_id
    
    # Получаем информацию об уроке
    lesson = await LessonRepository.get_by_id(lesson_id)
    if not lesson:
        await callback.message.edit_text(
            text="❌ Урок не найден!",
            reply_markup=get_home_kb()
        )
        return
    
    data = await state.get_data()
    subject_name = data['subject_name']
    
    # Сохраняем данные для удаления
    await state.update_data(lesson_id=lesson_id, lesson_name=lesson.name)
    
    await state.set_state(ManagerLessonStates.confirm_deletion)
    await callback.message.edit_text(
        text=f"❗️ Вы уверены, что хотите удалить урок \"{lesson.name}\" из предмета {subject_name}?",
        reply_markup=confirm_delete_lesson_kb(lesson_id)
    )

@router.callback_query(LessonCallback.filter(F.action == LessonActions.CONFIRM_DELETE), StateFilter(ManagerLessonStates.confirm_deletion))
async def process_delete_lesson(callback: CallbackQuery, callback_data: LessonCallback, state: FSMContext):
    """Удаляем урок после подтверждения"""
    data = await state.get_data()
    lesson_id = data['lesson_id']
    lesson_name = data['lesson_name']
    subject_id = data['subject_id']
    subject_name = data['subject_name']
    
    # Удаляем урок из базы данных
    success = await LessonRepository.delete(lesson_id)
    
    if success:
        # Получаем обновленный список уроков
        lessons = await LessonRepository.get_by_subject_and_course(subject_id, data['course_id'])
        
        await state.set_state(ManagerLessonStates.lessons_list)
        await callback.message.edit_text(
            text=f"✅ Урок \"{lesson_name}\" успешно удален из предмета {subject_name}\n"
                 f"Всего уроков: {len(lessons)}",
            reply_markup=await get_lessons_list_kb(
                lessons,
                course_id=data['course_id'],
                subject_id=subject_id
            )
        )
    else:
        await callback.message.edit_text(
            text=f"❌ Ошибка при удалении урока \"{lesson_name}\"",
            reply_markup=get_home_kb()
        )

@router.callback_query(LessonCallback.filter(F.action == LessonActions.CANCEL), StateFilter(ManagerLessonStates.confirm_deletion))
async def cancel_action(callback: CallbackQuery, callback_data: LessonCallback, state: FSMContext):
    """Отмена действия"""
    await back_to_lessons_list(callback, state)

# === РЕДАКТИРОВАНИЕ УРОКОВ ===

@router.callback_query(LessonCallback.filter(F.action == LessonActions.EDIT), StateFilter(ManagerLessonStates.lessons_list))
async def start_edit_lesson(callback: CallbackQuery, callback_data: LessonCallback, state: FSMContext):
    """Начать редактирование урока"""
    lesson_id = callback_data.lesson_id

    try:
        # Получаем урок из БД
        lesson = await LessonRepository.get_by_id(lesson_id)
        if not lesson:
            await callback.message.edit_text(
                text="❌ Урок не найден!",
                reply_markup=get_home_kb()
            )
            return

        # Сохраняем данные урока в состоянии
        await state.update_data(
            lesson_to_edit=lesson_id,
            current_lesson_name=lesson.name
        )
        await state.set_state(ManagerLessonStates.edit_lesson)

        # Создаем клавиатуру для редактирования
        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
        edit_kb = InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="✏️ Изменить название", callback_data="change_lesson_name")],
            [InlineKeyboardButton(text="⬅️ Назад к списку", callback_data="back_to_lessons_list")],
            *get_home_kb().inline_keyboard
        ])

        await callback.message.edit_text(
            text=f"📝 Редактирование урока\n\n"
                 f"📖 Название: {lesson.name}\n"
                 f"🆔 ID: {lesson.id}\n\n"
                 f"Выберите действие:",
            reply_markup=edit_kb
        )

    except Exception as e:
        logger.error(f"Ошибка при начале редактирования урока: {e}")
        await callback.message.edit_text(
            text="❌ Ошибка при загрузке данных урока!",
            reply_markup=get_home_kb()
        )

@router.callback_query(F.data == "change_lesson_name", StateFilter(ManagerLessonStates.edit_lesson))
async def start_change_lesson_name(callback: CallbackQuery, state: FSMContext):
    """Начать изменение названия урока"""
    data = await state.get_data()
    current_name = data.get("current_lesson_name", "")

    await callback.message.edit_text(
        text=f"✏️ Изменение названия урока\n\n"
             f"Текущее название: {current_name}\n\n"
             f"Введите новое название урока:",
        reply_markup=get_home_kb()
    )
    await state.set_state(ManagerLessonStates.enter_new_lesson_name)

@router.message(StateFilter(ManagerLessonStates.enter_new_lesson_name))
async def process_new_lesson_name(message: Message, state: FSMContext):
    """Обработать ввод нового названия урока"""
    new_lesson_name = message.text.strip()
    data = await state.get_data()
    current_name = data.get("current_lesson_name", "")
    lesson_id = data.get("lesson_to_edit")
    course_id = data.get("course_id")
    subject_id = data.get("subject_id")

    # Проверяем, не совпадает ли новое название с текущим
    if new_lesson_name == current_name:
        await message.answer(
            text=f"⚠️ Новое название совпадает с текущим!\n\n"
                 f"Введите другое название урока:",
            reply_markup=get_home_kb()
        )
        return

    # Проверяем, существует ли уже урок с таким названием в рамках курса и предмета
    try:
        existing_lesson = await LessonRepository.get_by_name_subject_and_course(
            new_lesson_name, subject_id, course_id
        )
        if existing_lesson:
            await message.answer(
                text=f"❌ Урок с названием '{new_lesson_name}' уже существует в данном курсе и предмете!\n\n"
                     f"Введите другое название урока:",
                reply_markup=get_home_kb()
            )
            return
    except Exception as e:
        logger.error(f"Ошибка при проверке существования урока: {e}")
        await message.answer(
            text=f"❌ Ошибка при проверке существования урока!\n\n"
                 f"Попробуйте еще раз:",
            reply_markup=get_home_kb()
        )
        return

    await state.update_data(new_lesson_name=new_lesson_name)
    await state.set_state(ManagerLessonStates.confirm_edit_lesson)

    await message.answer(
        text=f"📋 Подтверждение изменения урока:\n\n"
             f"Текущее название: {current_name}\n"
             f"Новое название: {new_lesson_name}",
        reply_markup=confirm_edit_lesson_kb(lesson_id)
    )

@router.callback_query(LessonCallback.filter(F.action == LessonActions.CONFIRM_EDIT), StateFilter(ManagerLessonStates.confirm_edit_lesson))
async def confirm_edit_lesson(callback: CallbackQuery, callback_data: LessonCallback, state: FSMContext):
    """Подтвердить редактирование урока"""
    data = await state.get_data()
    lesson_id = data.get("lesson_to_edit")
    new_name = data.get("new_lesson_name", "")
    current_name = data.get("current_lesson_name", "")

    try:
        # Обновляем урок в базе данных
        updated_lesson = await LessonRepository.update(lesson_id, name=new_name)

        if updated_lesson:
            # Сохраняем контекст роли для правильной навигации
            await state.update_data(navigation_context="manager")
            await state.set_state(ManagerLessonStates.lessons_list)

            await callback.message.edit_text(
                text=f"✅ Урок успешно обновлен!\n\n"
                     f"Старое название: {current_name}\n"
                     f"Новое название: {new_name}",
                reply_markup=get_home_kb()
            )
        else:
            await callback.message.edit_text(
                text=f"❌ Ошибка при обновлении урока!",
                reply_markup=get_home_kb()
            )
    except Exception as e:
        logger.error(f"Ошибка при обновлении урока: {e}")
        await callback.message.edit_text(
            text=f"❌ Ошибка при обновлении урока!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )

@router.callback_query(F.data == "back_to_lessons_list", StateFilter(ManagerLessonStates.edit_lesson))
async def back_to_lessons_list_from_edit(callback: CallbackQuery, state: FSMContext):
    """Вернуться к списку уроков из режима редактирования"""
    await back_to_lessons_list(callback, state)

@router.callback_query(LessonCallback.filter(F.action == LessonActions.CANCEL), StateFilter(ManagerLessonStates.confirm_edit_lesson))
async def cancel_edit_lesson(callback: CallbackQuery, callback_data: LessonCallback, state: FSMContext):
    """Отменить редактирование урока"""
    await callback.message.edit_text(
        text="❌ Редактирование урока отменено",
        reply_markup=get_home_kb()
    )
    # Устанавливаем состояние главного меню менеджера для правильной навигации
    await state.set_state(ManagerMainStates.main)

@router.callback_query(F.data == "back_to_lessons_list", StateFilter(ManagerLessonStates.edit_lesson))
async def back_to_lessons_list_from_edit(callback: CallbackQuery, state: FSMContext):
    """Вернуться к списку уроков из режима редактирования"""
    await back_to_lessons_list(callback, state)

@router.callback_query(LessonCallback.filter(F.action == LessonActions.CANCEL), StateFilter(ManagerLessonStates.confirm_edit_lesson))
async def cancel_edit_lesson(callback: CallbackQuery, callback_data: LessonCallback, state: FSMContext):
    """Отменить редактирование урока"""
    await callback.message.edit_text(
        text="❌ Редактирование урока отменено",
        reply_markup=get_home_kb()
    )
    # Устанавливаем состояние главного меню менеджера для правильной навигации
    from manager.handlers.main import ManagerMainStates
    await state.set_state(ManagerMainStates.main)